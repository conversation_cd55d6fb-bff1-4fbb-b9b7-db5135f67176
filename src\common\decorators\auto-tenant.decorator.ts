import { SetMetadata } from '@nestjs/common';
import { tenantContext } from '../subscribers/tenant-entity.subscriber';

export const AUTO_TENANT_KEY = 'auto_tenant';

/**
 * Decorator để tự động inject tenantId vào method
 * Sử dụng cho các method trong repository hoặc service
 */
export const AutoTenant = () => SetMetadata(AUTO_TENANT_KEY, true);

/**
 * Helper function để tự động thêm tenantId vào query options
 */
export function addTenantToOptions<T>(options: any = {}): any {
  const store = tenantContext.getStore();
  const tenantId = store?.tenantId;
  
  if (!tenantId || store?.disableTenantFilter) {
    return options;
  }

  // Nếu chưa có where, tạo mới
  if (!options.where) {
    options.where = { tenantId };
    return options;
  }

  // Nếu where là object
  if (typeof options.where === 'object' && !Array.isArray(options.where)) {
    if (!options.where.tenantId) {
      options.where.tenantId = tenantId;
    }
    return options;
  }

  // Nếu where là array
  if (Array.isArray(options.where)) {
    options.where = options.where.map((condition: any) => {
      if (typeof condition === 'object' && !condition.tenantId) {
        return { ...condition, tenantId };
      }
      return condition;
    });
    return options;
  }

  return options;
}

/**
 * Helper function để tự động thêm tenantId vào QueryBuilder
 */
export function addTenantToQueryBuilder(queryBuilder: any, alias: string = 'entity'): any {
  const store = tenantContext.getStore();
  const tenantId = store?.tenantId;

  console.log(`[addTenantToQueryBuilder] Store: ${JSON.stringify(store)}, tenantId: ${tenantId}`);

  if (!tenantId || store?.disableTenantFilter) {
    console.log(`[addTenantToQueryBuilder] Skipping tenant filter - tenantId: ${tenantId}, disabled: ${store?.disableTenantFilter}`);
    return queryBuilder;
  }

  // Kiểm tra xem đã có điều kiện tenantId chưa
  try {
    const sql = queryBuilder.getSql();
    if (sql.includes(`${alias}.tenantId`) || sql.includes(`${alias}.tenant_id`)) {
      console.log(`[addTenantToQueryBuilder] TenantId condition already exists in SQL: ${sql}`);
      return queryBuilder;
    }
  } catch (error) {
    console.log(`[addTenantToQueryBuilder] Could not get SQL, proceeding with andWhere`);
  }

  console.log(`[addTenantToQueryBuilder] Adding tenantId condition: ${alias}.tenantId = ${tenantId}`);
  return queryBuilder.andWhere(`${alias}.tenantId = :tenantId`, { tenantId });
}
