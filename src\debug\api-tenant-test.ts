import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { JwtUtilService } from '../modules/auth/guards/jwt.util';
import { TokenType } from '../modules/auth/enums/token-type.enum';
import { Logger } from '@nestjs/common';

/**
 * Script test API với tenant isolation
 * Chạy: npm run test:api-tenant
 */
async function testApiWithTenant() {
  const logger = new Logger('ApiTenantTest');
  let app: INestApplication;
  
  try {
    // Khởi tạo ứng dụng NestJS
    app = await NestFactory.create(AppModule);
    await app.init();
    
    const jwtUtil = app.get(JwtUtilService);
    
    logger.log('=== API TENANT TEST ===');
    
    // Tạo JWT token cho user với tenantId = 1
    const user1Token = jwtUtil.generateToken({
      id: 1,
      sub: 1,
      tenantId: 1,
      type: 'EMPLOYEE',
      username: '<EMAIL>'
    }, TokenType.ACCESS);
    
    // Tạo JWT token cho user với tenantId = 2
    const user2Token = jwtUtil.generateToken({
      id: 2,
      sub: 2,
      tenantId: 2,
      type: 'EMPLOYEE',
      username: '<EMAIL>'
    }, TokenType.ACCESS);
    
    // Tạo JWT token cho SYSTEM_ADMIN
    const adminToken = jwtUtil.generateToken({
      id: 999,
      sub: 999,
      tenantId: 1,
      type: 'SYSTEM_ADMIN',
      username: '<EMAIL>'
    }, TokenType.ACCESS);
    
    logger.log('Generated test tokens');
    
    // Test 1: User 1 truy cập todos
    logger.log('\n--- Test 1: User 1 (tenantId=1) truy cập todos ---');
    try {
      const response1 = await request(app.getHttpServer())
        .get('/api/todos')
        .set('Authorization', `Bearer ${user1Token}`)
        .expect(200);
      
      logger.log(`User 1 todos count: ${response1.body.data?.items?.length || 0}`);
      if (response1.body.data?.items?.length > 0) {
        const tenantIds = [...new Set(response1.body.data.items.map((item: any) => item.tenantId))];
        logger.log(`User 1 todos tenantIds: ${JSON.stringify(tenantIds)}`);
      }
    } catch (error) {
      logger.error(`Test 1 failed: ${error.message}`);
    }
    
    // Test 2: User 2 truy cập todos
    logger.log('\n--- Test 2: User 2 (tenantId=2) truy cập todos ---');
    try {
      const response2 = await request(app.getHttpServer())
        .get('/api/todos')
        .set('Authorization', `Bearer ${user2Token}`)
        .expect(200);
      
      logger.log(`User 2 todos count: ${response2.body.data?.items?.length || 0}`);
      if (response2.body.data?.items?.length > 0) {
        const tenantIds = [...new Set(response2.body.data.items.map((item: any) => item.tenantId))];
        logger.log(`User 2 todos tenantIds: ${JSON.stringify(tenantIds)}`);
      }
    } catch (error) {
      logger.error(`Test 2 failed: ${error.message}`);
    }
    
    // Test 3: User 1 tạo todo mới
    logger.log('\n--- Test 3: User 1 tạo todo mới ---');
    try {
      const newTodo = {
        title: 'Test Todo from User 1',
        description: 'This should have tenantId = 1',
        assigneeId: 1,
        priority: 'medium'
      };
      
      const response3 = await request(app.getHttpServer())
        .post('/api/todos')
        .set('Authorization', `Bearer ${user1Token}`)
        .send(newTodo)
        .expect(201);
      
      logger.log(`Created todo: ${JSON.stringify(response3.body.data)}`);
      logger.log(`Created todo tenantId: ${response3.body.data?.tenantId}`);
      
      // Cleanup - xóa todo vừa tạo
      if (response3.body.data?.id) {
        await request(app.getHttpServer())
          .delete(`/api/todos/${response3.body.data.id}`)
          .set('Authorization', `Bearer ${user1Token}`);
        logger.log(`Cleaned up todo ${response3.body.data.id}`);
      }
    } catch (error) {
      logger.error(`Test 3 failed: ${error.message}`);
    }
    
    // Test 4: User 2 tạo todo mới
    logger.log('\n--- Test 4: User 2 tạo todo mới ---');
    try {
      const newTodo = {
        title: 'Test Todo from User 2',
        description: 'This should have tenantId = 2',
        assigneeId: 2,
        priority: 'high'
      };
      
      const response4 = await request(app.getHttpServer())
        .post('/api/todos')
        .set('Authorization', `Bearer ${user2Token}`)
        .send(newTodo)
        .expect(201);
      
      logger.log(`Created todo: ${JSON.stringify(response4.body.data)}`);
      logger.log(`Created todo tenantId: ${response4.body.data?.tenantId}`);
      
      // Cleanup - xóa todo vừa tạo
      if (response4.body.data?.id) {
        await request(app.getHttpServer())
          .delete(`/api/todos/${response4.body.data.id}`)
          .set('Authorization', `Bearer ${user2Token}`);
        logger.log(`Cleaned up todo ${response4.body.data.id}`);
      }
    } catch (error) {
      logger.error(`Test 4 failed: ${error.message}`);
    }
    
    // Test 5: SYSTEM_ADMIN truy cập todos
    logger.log('\n--- Test 5: SYSTEM_ADMIN truy cập todos ---');
    try {
      const response5 = await request(app.getHttpServer())
        .get('/api/todos')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);
      
      logger.log(`Admin todos count: ${response5.body.data?.items?.length || 0}`);
      if (response5.body.data?.items?.length > 0) {
        const tenantIds = [...new Set(response5.body.data.items.map((item: any) => item.tenantId))];
        logger.log(`Admin todos tenantIds: ${JSON.stringify(tenantIds)}`);
      }
    } catch (error) {
      logger.error(`Test 5 failed: ${error.message}`);
    }
    
    logger.log('\n=== API TENANT TEST COMPLETED ===');
    
  } catch (error) {
    logger.error(`Test script error: ${error.message}`);
    logger.error(error.stack);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

// Chạy script nếu được gọi trực tiếp
if (require.main === module) {
  testApiWithTenant()
    .then(() => {
      console.log('API test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('API test failed:', error);
      process.exit(1);
    });
}

export { testApiWithTenant };
