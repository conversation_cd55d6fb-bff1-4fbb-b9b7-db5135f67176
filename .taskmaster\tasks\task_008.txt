# Task ID: 8
# Title: Update Todolists Module - Project Repository
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Add tenantId filtering to all Project repository methods
# Details:
Update ProjectRepository methods:
- findAll(): Add tenantId filtering
- findById(): Add tenantId to WHERE condition
- findByOwnerId(): Add tenantId filtering
- create(): Set tenantId on new entities
- update(): Add tenantId to WHERE condition
- delete(): Add tenantId to WHERE condition
- getProjectStats(): Add tenantId to aggregation queries

# Test Strategy:
Unit tests for Project repository with tenantId validation
