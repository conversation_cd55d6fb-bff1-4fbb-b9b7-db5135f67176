import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Objective } from '../entities/objective.entity';
import { ObjectiveQueryDto } from '../dto/objective/objective-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { addTenantToOptions, addTenantToQueryBuilder } from '@/common/decorators/auto-tenant.decorator';

/**
 * Repository cho mục tiêu với tenant isolation đơn giản
 */
@Injectable()
export class ObjectiveRepository {
  private readonly logger = new Logger(ObjectiveRepository.name);

  constructor(
    @InjectRepository(Objective)
    private readonly repository: Repository<Objective>,
  ) {}

  /**
   * Tìm tất cả mục tiêu với phân trang và lọc
   * @param query Tham số truy vấn
   * @returns Danh sách phân trang các mục tiêu
   */
  async findAll(query: ObjectiveQueryDto): Promise<PaginatedResult<Objective>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      type,
      cycleId,
      ownerId,
      departmentId,
      parentId,
      startDate,
      endDate,
    } = query;

    // Tạo queryBuilder và tự động thêm tenantId
    const queryBuilder = this.repository.createQueryBuilder('objective');
    addTenantToQueryBuilder(queryBuilder, 'objective');

    // Áp dụng bộ lọc nếu được cung cấp
    if (type) {
      queryBuilder.andWhere('objective.type = :type', { type });
    }

    if (cycleId) {
      queryBuilder.andWhere('objective.cycleId = :cycleId', { cycleId });
    }

    if (ownerId) {
      queryBuilder.andWhere('objective.ownerId = :ownerId', { ownerId });
    }

    if (departmentId) {
      queryBuilder.andWhere('objective.departmentId = :departmentId', {
        departmentId,
      });
    }

    if (parentId) {
      queryBuilder.andWhere('objective.parentId = :parentId', { parentId });
    } else if (parentId === null) {
      queryBuilder.andWhere('objective.parentId IS NULL');
    }

    // Áp dụng bộ lọc ngày bắt đầu nếu được cung cấp
    if (startDate) {
      queryBuilder.andWhere('objective.startDate >= :startDate', { startDate });
    }

    // Áp dụng bộ lọc ngày kết thúc nếu được cung cấp
    if (endDate) {
      queryBuilder.andWhere('objective.endDate <= :endDate', { endDate });
    }

    // Áp dụng bộ lọc tìm kiếm nếu được cung cấp
    if (search) {
      queryBuilder.andWhere(
        'objective.title ILIKE :search OR objective.description ILIKE :search',
        { search: `%${search}%` },
      );
    }

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`objective.${sortBy}`, sortDirection);

    // Áp dụng phân trang
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm mục tiêu theo ID
   * @param id ID mục tiêu
   * @returns Mục tiêu hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<Objective | null> {
    // Tự động thêm tenantId vào options
    const options = addTenantToOptions({ where: { id } });
    return this.repository.findOne(options);
  }

  /**
   * Tạo mới mục tiêu
   * @param data Dữ liệu mục tiêu
   * @returns Mục tiêu đã tạo
   */
  async create(data: Partial<Objective>): Promise<Objective> {
    const objective = this.repository.create(data);
    return this.repository.save(objective);
  }

  /**
   * Cập nhật mục tiêu
   * @param id ID mục tiêu
   * @param data Dữ liệu mục tiêu cập nhật
   * @returns Mục tiêu đã cập nhật hoặc null nếu không tìm thấy
   */
  async update(
    id: number,
    data: Partial<Objective>,
  ): Promise<Objective | null> {
    // Tự động thêm tenantId vào where condition
    const whereOptions = addTenantToOptions({ id });
    await this.repository.update(whereOptions, data);
    return this.findById(id);
  }

  /**
   * Xóa mục tiêu
   * @param id ID mục tiêu
   * @returns True nếu xóa thành công, false nếu không tìm thấy
   */
  async delete(id: number): Promise<boolean> {
    // Tự động thêm tenantId vào where condition
    const whereOptions = addTenantToOptions({ id });
    const result = await this.repository.delete(whereOptions);
    return (result.affected ?? 0) > 0;
  }

  /**
   * Tìm mục tiêu theo ID chu kỳ
   * @param cycleId ID chu kỳ OKR
   * @returns Danh sách mục tiêu
   */
  async findByCycleId(cycleId: number): Promise<Objective[]> {
    // Tự động thêm tenantId vào options
    const options = addTenantToOptions({
      where: { cycleId },
      order: { createdAt: 'DESC' },
    });
    return this.repository.find(options);
  }

  /**
   * Tìm mục tiêu theo ID chủ sở hữu
   * @param ownerId ID chủ sở hữu
   * @returns Danh sách mục tiêu
   */
  async findByOwnerId(ownerId: number): Promise<Objective[]> {
    // Tự động thêm tenantId vào options
    const options = addTenantToOptions({
      where: { ownerId },
      order: { createdAt: 'DESC' },
    });
    return this.repository.find(options);
  }

  /**
   * Tìm mục tiêu con theo ID mục tiêu cha
   * @param parentId ID mục tiêu cha
   * @returns Danh sách mục tiêu con
   */
  async findByParentId(parentId: number): Promise<Objective[]> {
    // Tự động thêm tenantId vào options
    const options = addTenantToOptions({
      where: { parentId },
      order: { createdAt: 'DESC' },
    });
    return this.repository.find(options);
  }

  /**
   * Cập nhật tiến độ mục tiêu
   * @param id ID mục tiêu
   * @param progress Giá trị tiến độ (0-100)
   * @returns Mục tiêu đã cập nhật hoặc null nếu không tìm thấy
   */
  async updateProgress(
    id: number,
    progress: number,
  ): Promise<Objective | null> {
    // Tự động thêm tenantId vào where condition
    const whereOptions = addTenantToOptions({ id });
    await this.repository.update(whereOptions, { progress, updatedAt: Date.now() });
    return this.findById(id);
  }
}
