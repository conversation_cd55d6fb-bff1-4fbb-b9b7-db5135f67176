# Task ID: 17
# Title: Update System Module - Notification Repository
# Status: pending
# Dependencies: 1
# Priority: medium
# Description: Add tenantId filtering to System Notification repository methods
# Details:
Update NotificationRepository methods:
- findAll(): Add tenantId to QueryBuilder
- findById(): Add tenantId to WHERE condition
- findByUserId(): Add tenantId filtering
- markAsRead(): Add tenantId to WHERE condition
- create(): Set tenantId on new entities
- delete(): Add tenantId to WHERE condition
- getUnreadCount(): Add tenantId to count queries

# Test Strategy:
Unit tests for Notification repository
