import { Injectable, Logger } from '@nestjs/common';
import { ObjectiveRepository } from '../repositories/objective.repository';
import { OkrCycleRepository } from '../repositories/okr-cycle.repository';
import { Objective } from '../entities/objective.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common';
import { OKRS_ERROR_CODES } from '../errors/okrs-error.code';
import { CreateObjectiveDto } from '../dto/objective/create-objective.dto';
import { ObjectiveResponseDto } from '../dto/objective/objective-response.dto';
import { ObjectiveQueryDto } from '../dto/objective/objective-query.dto';
import { UpdateObjectiveDto } from '../dto/objective/update-objective.dto';

/**
 * Service for objectives
 */
@Injectable()
export class ObjectiveService {
  private readonly logger = new Logger(ObjectiveService.name);

  constructor(
    private readonly objectiveRepository: ObjectiveRepository,
    private readonly okrCycleRepository: OkrCycleRepository,
  ) {}

  /**
   * Create a new objective
   * @param userId User ID creating the objective
   * @param dto Create objective DTO
   * @returns Created objective response
   */
  async create(
    userId: number,
    dto: CreateObjectiveDto,
  ): Promise<ObjectiveResponseDto> {
    // Validate date range
    if (new Date(dto.startDate) > new Date(dto.endDate)) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_INVALID_DATE_RANGE,
        'Ngày bắt đầu phải trước ngày kết thúc',
      );
    }

    // Validate cycle exists
    const cycle = await this.okrCycleRepository.findById(dto.cycleId);
    if (!cycle) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_INVALID_CYCLE,
        `Không tìm thấy chu kỳ OKR với ID ${dto.cycleId}`,
      );
    }

    // Validate parent objective exists if provided
    if (dto.parentId) {
      const parentObjective = await this.objectiveRepository.findById(
        this.getTenantId(),
        dto.parentId,
      );
      if (!parentObjective) {
        throw new AppException(
          OKRS_ERROR_CODES.OBJECTIVE_INVALID_PARENT,
          `Không tìm thấy mục tiêu cha với ID ${dto.parentId}`,
        );
      }
    }

    // Chuẩn bị dữ liệu tạo mới
    const createData: Partial<Objective> = {
      title: dto.title,
      description: dto.description,
      ownerId: dto.ownerId,
      departmentId: dto.departmentId,
      parentId: dto.parentId,
      cycleId: dto.cycleId,
      type: dto.type,
      createdBy: userId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      progress: 0,
      status: 'active',
    };

    // Chuyển đổi các trường ngày thành đối tượng Date
    createData.startDate = new Date(dto.startDate);
    createData.endDate = new Date(dto.endDate);

    // Create objective
    const objective = await this.objectiveRepository.create(this.getTenantId(), createData);

    return this.mapToResponseDto(objective);
  }

  /**
   * Get all objectives with pagination and filtering
   * @param query Query parameters
   * @returns Paginated list of objective responses
   */
  async findAll(
    tenantId: number,
    query: ObjectiveQueryDto,
  ): Promise<PaginatedResult<ObjectiveResponseDto>> {
    console.log(`[ObjectiveService.findAll] Called with tenantId: ${tenantId}`);

    const result = await this.objectiveRepository.findAll(tenantId, query);

    return {
      items: result.items.map((objective) => this.mapToResponseDto(objective)),
      meta: result.meta,
    };
  }

  /**
   * Get an objective by ID
   * @param id Objective ID
   * @returns Objective response
   */
  async findById(id: number): Promise<ObjectiveResponseDto> {
    const objective = await this.objectiveRepository.findById(this.getTenantId(), id);

    if (!objective) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_NOT_FOUND,
        `Không tìm thấy mục tiêu với ID ${id}`,
      );
    }

    return this.mapToResponseDto(objective);
  }

  /**
   * Update an objective
   * @param id Objective ID
   * @param dto Update objective DTO
   * @returns Updated objective response
   */
  async update(
    id: number,
    dto: UpdateObjectiveDto,
  ): Promise<ObjectiveResponseDto> {
    // Check if objective exists
    const existingObjective = await this.objectiveRepository.findById(this.getTenantId(), id);

    if (!existingObjective) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_NOT_FOUND,
        `Không tìm thấy mục tiêu với ID ${id}`,
      );
    }

    // Validate date range if both dates are provided
    if (dto.startDate && dto.endDate) {
      if (new Date(dto.startDate) > new Date(dto.endDate)) {
        throw new AppException(
          OKRS_ERROR_CODES.OBJECTIVE_INVALID_DATE_RANGE,
          'Ngày bắt đầu phải trước ngày kết thúc',
        );
      }
    } else if (dto.startDate && !dto.endDate) {
      // If only start date is provided, check against existing end date
      if (new Date(dto.startDate) > new Date(existingObjective.endDate)) {
        throw new AppException(
          OKRS_ERROR_CODES.OBJECTIVE_INVALID_DATE_RANGE,
          'Ngày bắt đầu phải trước ngày kết thúc',
        );
      }
    } else if (!dto.startDate && dto.endDate) {
      // If only end date is provided, check against existing start date
      if (new Date(existingObjective.startDate) > new Date(dto.endDate)) {
        throw new AppException(
          OKRS_ERROR_CODES.OBJECTIVE_INVALID_DATE_RANGE,
          'Ngày bắt đầu phải trước ngày kết thúc',
        );
      }
    }

    // Validate parent objective exists if provided
    if (dto.parentId) {
      // Check for self-reference
      if (dto.parentId === id) {
        throw new AppException(
          OKRS_ERROR_CODES.OBJECTIVE_INVALID_PARENT,
          'Mục tiêu không thể là mục tiêu cha của chính nó',
        );
      }

      const parentObjective = await this.objectiveRepository.findById(
        this.getTenantId(),
        dto.parentId,
      );
      if (!parentObjective) {
        throw new AppException(
          OKRS_ERROR_CODES.OBJECTIVE_INVALID_PARENT,
          `Không tìm thấy mục tiêu cha với ID ${dto.parentId}`,
        );
      }
    }

    // Chuẩn bị dữ liệu cập nhật
    const updateData: Partial<Objective> = {
      title: dto.title,
      description: dto.description,
      ownerId: dto.ownerId,
      departmentId: dto.departmentId,
      parentId: dto.parentId,
      status: dto.status,
      type: dto.type,
      updatedAt: Date.now(),
    };

    // Chuyển đổi các trường ngày thành đối tượng Date nếu có
    if (dto.startDate) {
      updateData.startDate = new Date(dto.startDate);
    }

    if (dto.endDate) {
      updateData.endDate = new Date(dto.endDate);
    }

    // Update objective
    const updatedObjective = await this.objectiveRepository.update(
      this.getTenantId(),
      id,
      updateData,
    );

    if (!updatedObjective) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_NOT_FOUND,
        `Không tìm thấy mục tiêu với ID ${id}`,
      );
    }

    return this.mapToResponseDto(updatedObjective);
  }

  /**
   * Delete an objective
   * @param id Objective ID
   * @returns True if deleted
   */
  async delete(id: number): Promise<boolean> {
    // Check if objective exists
    const existingObjective = await this.objectiveRepository.findById(this.getTenantId(), id);

    if (!existingObjective) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_NOT_FOUND,
        `Không tìm thấy mục tiêu với ID ${id}`,
      );
    }

    // Delete objective
    const deleted = await this.objectiveRepository.delete(this.getTenantId(), id);

    if (!deleted) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_NOT_FOUND,
        `Không tìm thấy mục tiêu với ID ${id}`,
      );
    }

    return true;
  }

  /**
   * Update objective progress
   * @param id Objective ID
   * @param progress Progress value (0-100)
   * @returns Updated objective response
   */
  async updateProgress(
    id: number,
    progress: number,
  ): Promise<ObjectiveResponseDto> {
    // Check if objective exists
    const existingObjective = await this.objectiveRepository.findById(this.getTenantId(), id);

    if (!existingObjective) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_NOT_FOUND,
        `Không tìm thấy mục tiêu với ID ${id}`,
      );
    }

    // Validate progress value
    if (progress < 0 || progress > 100) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_INVALID_OWNER, // Sử dụng mã lỗi khác vì OBJECTIVE_INVALID_PROGRESS không tồn tại
        'Giá trị tiến độ phải từ 0 đến 100',
      );
    }

    // Update objective progress
    const updatedObjective = await this.objectiveRepository.updateProgress(
      this.getTenantId(),
      id,
      progress,
    );

    if (!updatedObjective) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_NOT_FOUND,
        `Không tìm thấy mục tiêu với ID ${id}`,
      );
    }

    return this.mapToResponseDto(updatedObjective);
  }

  /**
   * Map objective entity to response DTO
   * @param objective Objective entity
   * @returns Objective response DTO
   */
  private mapToResponseDto(objective: Objective): ObjectiveResponseDto {
    const response = new ObjectiveResponseDto();

    response.id = objective.id;
    response.title = objective.title;
    response.description = objective.description;
    response.ownerId = objective.ownerId;
    response.departmentId = objective.departmentId;
    response.parentId = objective.parentId;
    response.cycleId = objective.cycleId;
    response.type = objective.type;
    response.progress = objective.progress;
    response.status = objective.status;

    // Xử lý startDate an toàn
    if (objective.startDate instanceof Date) {
      response.startDate = objective.startDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    } else if (typeof objective.startDate === 'string') {
      // Nếu là chuỗi, giữ nguyên hoặc chuyển đổi nếu cần
      response.startDate = objective.startDate;
    } else {
      // Trường hợp null, undefined hoặc kiểu dữ liệu khác
      response.startDate = ''; // Hoặc một giá trị mặc định khác
      this.logger.warn(
        `Invalid startDate for objective ID ${objective.id}: ${objective.startDate}`,
      );
    }

    // Xử lý endDate an toàn
    if (objective.endDate instanceof Date) {
      response.endDate = objective.endDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    } else if (typeof objective.endDate === 'string') {
      // Nếu là chuỗi, giữ nguyên hoặc chuyển đổi nếu cần
      response.endDate = objective.endDate;
    } else {
      // Trường hợp null, undefined hoặc kiểu dữ liệu khác
      response.endDate = ''; // Hoặc một giá trị mặc định khác
      this.logger.warn(
        `Invalid endDate for objective ID ${objective.id}: ${objective.endDate}`,
      );
    }

    response.createdBy = objective.createdBy;
    response.createdAt = objective.createdAt;
    response.updatedAt = objective.updatedAt;

    return response;
  }

  /**
   * TEMPORARY: Get tenantId - will be replaced with proper context extraction
   */
  private getTenantId(): number {
    // TODO: Extract from request context or parameter
    return 1; // Hard-coded for now
  }
}
