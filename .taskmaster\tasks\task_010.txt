# Task ID: 10
# Title: Update HRM Module - Employee Repository
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Add tenantId filtering to all Employee repository methods
# Details:
Update EmployeeRepository methods:
- findAll(): Add tenantId to QueryBuilder
- findById(): Add tenantId to WHERE condition
- findByDepartmentId(): Add tenantId filtering
- findByPositionId(): Add tenantId filtering
- create(): Set tenantId on new entities
- update(): Add tenantId to WHERE condition
- delete(): Add tenantId to WHERE condition
- searchEmployees(): Add tenantId to search queries

# Test Strategy:
Unit tests to ensure Employee data isolation
