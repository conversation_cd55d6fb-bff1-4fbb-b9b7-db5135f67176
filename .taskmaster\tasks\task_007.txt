# Task ID: 7
# Title: Update Todolists Module - Todo Repository
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Add tenantId filtering to all Todo repository methods
# Details:
Update TodoRepository methods:
- findAll(): Add tenantId to QueryBuilder WHERE clause
- findById(): Add tenantId to WHERE condition
- findByProjectId(): Add tenantId filtering
- findByAssigneeId(): Add tenantId filtering
- create(): Set tenantId on new entities
- update(): Add tenantId to WHERE condition
- delete(): Add tenantId to WHERE condition
- search(): Add tenantId to search queries

# Test Strategy:
Unit tests to verify Todo isolation by tenant
