import { Repository, SelectQueryBuilder, FindManyOptions, FindOneOptions, DataSource } from 'typeorm';
import { Logger } from '@nestjs/common';
import { tenantContext } from '../subscribers/tenant-entity.subscriber';

/**
 * Base Repository với tenant isolation tự động
 * Tất cả repository phải extend từ class này để đảm bảo tenant isolation
 */
export abstract class BaseTenantRepository<T> extends Repository<T> {
  protected readonly logger = new Logger(this.constructor.name);

  constructor(
    protected readonly dataSource: DataSource,
    entityClass: new () => T,
  ) {
    super(entityClass, dataSource.createEntityManager());
  }

  /**
   * Lấy tenantId từ context hiện tại
   */
  private getCurrentTenantId(): number | undefined {
    const store = tenantContext.getStore();
    
    this.logger.debug(`[getCurrentTenantId] Current store: ${JSON.stringify(store)}`);

    // Nếu disableTenantFilter = true, trả về undefined để không thêm điều kiện tenantId
    if (store?.disableTenantFilter) {
      this.logger.debug('[getCurrentTenantId] Tenant filter disabled, returning undefined');
      return undefined;
    }

    const tenantId = store?.tenantId;
    this.logger.debug(`[getCurrentTenantId] Returning tenantId: ${tenantId}`);
    return tenantId;
  }

  /**
   * Kiểm tra entity có trường tenantId không
   */
  private hasTenantIdField(): boolean {
    const metadata = this.dataSource.getMetadata(this.target);
    return metadata.columns.some((column) => column.propertyName === 'tenantId');
  }

  /**
   * Thêm điều kiện tenantId vào QueryBuilder
   */
  private addTenantConditionToQueryBuilder(queryBuilder: SelectQueryBuilder<T>): SelectQueryBuilder<T> {
    if (!this.hasTenantIdField()) {
      this.logger.debug(`[addTenantCondition] Entity ${this.metadata.name} does not have tenantId field`);
      return queryBuilder;
    }

    const tenantId = this.getCurrentTenantId();
    if (tenantId === undefined) {
      this.logger.warn(`[addTenantCondition] No tenantId in context for entity ${this.metadata.name}`);
      return queryBuilder;
    }

    const alias = queryBuilder.expressionMap.mainAlias?.name || this.metadata.name.toLowerCase();
    
    // Kiểm tra xem đã có điều kiện tenantId chưa
    const hasTenantCondition = queryBuilder.expressionMap.wheres?.some(
      (where) => {
        if (where.condition && typeof where.condition === 'string') {
          return where.condition.includes(`${alias}.tenantId =`) || where.condition.includes(`${alias}.tenant_id =`);
        }
        return false;
      },
    );

    if (!hasTenantCondition) {
      queryBuilder.andWhere(`${alias}.tenantId = :tenantId`, { tenantId });
      this.logger.log(`[addTenantCondition] Added tenantId condition: ${alias}.tenantId = ${tenantId} for entity ${this.metadata.name}`);
    } else {
      this.logger.debug(`[addTenantCondition] Entity ${this.metadata.name} already has tenantId condition`);
    }

    return queryBuilder;
  }

  /**
   * Thêm điều kiện tenantId vào FindOptions
   */
  private addTenantConditionToFindOptions(options: FindManyOptions<T> | FindOneOptions<T> = {}): FindManyOptions<T> | FindOneOptions<T> {
    if (!this.hasTenantIdField()) {
      this.logger.debug(`[addTenantConditionToFindOptions] Entity ${this.metadata.name} does not have tenantId field`);
      return options;
    }

    const tenantId = this.getCurrentTenantId();
    if (tenantId === undefined) {
      this.logger.warn(`[addTenantConditionToFindOptions] No tenantId in context for entity ${this.metadata.name}`);
      return options;
    }

    this.logger.debug(`[addTenantConditionToFindOptions] Original options: ${JSON.stringify(options)}`);

    // Khởi tạo where nếu chưa có
    if (!options.where) {
      options.where = {};
    }

    // Nếu where là object, thêm tenantId vào
    if (typeof options.where === 'object' && !Array.isArray(options.where)) {
      // Chỉ thêm tenantId nếu chưa được chỉ định
      if ((options.where as any).tenantId === undefined) {
        (options.where as any).tenantId = tenantId;
        this.logger.log(`[addTenantConditionToFindOptions] Added tenantId ${tenantId} to where clause for entity ${this.metadata.name}`);
      } else {
        this.logger.debug(`[addTenantConditionToFindOptions] Entity ${this.metadata.name} already has tenantId in where clause: ${(options.where as any).tenantId}`);
      }
    }
    // Nếu where là array, thêm tenantId vào mỗi phần tử
    else if (Array.isArray(options.where)) {
      options.where = options.where.map((criteria: any) => {
        if (typeof criteria === 'object' && criteria.tenantId === undefined) {
          return { ...criteria, tenantId };
        }
        return criteria;
      });
      this.logger.log(`[addTenantConditionToFindOptions] Added tenantId ${tenantId} to array where clause for entity ${this.metadata.name}`);
    }

    this.logger.debug(`[addTenantConditionToFindOptions] Final options: ${JSON.stringify(options)}`);
    return options;
  }

  /**
   * Override createQueryBuilder để tự động thêm tenantId
   */
  createQueryBuilder(alias?: string): SelectQueryBuilder<T> {
    const queryBuilder = super.createQueryBuilder(alias);
    return this.addTenantConditionToQueryBuilder(queryBuilder);
  }

  /**
   * Override find để tự động thêm tenantId
   */
  async find(options?: FindManyOptions<T>): Promise<T[]> {
    const modifiedOptions = this.addTenantConditionToFindOptions(options);
    return super.find(modifiedOptions);
  }

  /**
   * Override findOne để tự động thêm tenantId
   */
  async findOne(options: FindOneOptions<T>): Promise<T | null> {
    const modifiedOptions = this.addTenantConditionToFindOptions(options);
    return super.findOne(modifiedOptions);
  }

  /**
   * Override findAndCount để tự động thêm tenantId
   */
  async findAndCount(options?: FindManyOptions<T>): Promise<[T[], number]> {
    const modifiedOptions = this.addTenantConditionToFindOptions(options);
    return super.findAndCount(modifiedOptions);
  }

  /**
   * Override count để tự động thêm tenantId
   */
  async count(options?: FindManyOptions<T>): Promise<number> {
    const modifiedOptions = this.addTenantConditionToFindOptions(options);
    return super.count(modifiedOptions);
  }

  /**
   * Override save để tự động set tenantId
   */
  async save<Entity extends T>(entity: Entity): Promise<Entity>;
  async save<Entity extends T>(entities: Entity[]): Promise<Entity[]>;
  async save<Entity extends T>(entityOrEntities: Entity | Entity[]): Promise<Entity | Entity[]> {
    if (!this.hasTenantIdField()) {
      return super.save(entityOrEntities as any);
    }

    const tenantId = this.getCurrentTenantId();
    if (tenantId === undefined) {
      this.logger.warn(`[save] No tenantId in context for entity ${this.metadata.name}`);
      return super.save(entityOrEntities as any);
    }

    // Set tenantId cho entity hoặc entities
    if (Array.isArray(entityOrEntities)) {
      entityOrEntities.forEach((entity: any) => {
        if (entity.tenantId === undefined) {
          entity.tenantId = tenantId;
          this.logger.log(`[save] Set tenantId ${tenantId} for entity ${this.metadata.name}`);
        }
      });
    } else {
      const entity = entityOrEntities as any;
      if (entity.tenantId === undefined) {
        entity.tenantId = tenantId;
        this.logger.log(`[save] Set tenantId ${tenantId} for entity ${this.metadata.name}`);
      }
    }

    return super.save(entityOrEntities as any);
  }

  /**
   * Method để tạm thời tắt tenant filtering
   */
  async withoutTenantFilter<R>(callback: () => Promise<R>): Promise<R> {
    const currentStore = tenantContext.getStore();
    const tenantId = currentStore?.tenantId ?? -1;

    return tenantContext.run({ tenantId, disableTenantFilter: true }, callback);
  }
}
