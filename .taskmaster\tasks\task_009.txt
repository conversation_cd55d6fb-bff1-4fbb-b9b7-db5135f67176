# Task ID: 9
# Title: Update Todolists Module - Services and Controllers
# Status: pending
# Dependencies: 7, 8
# Priority: high
# Description: Update Todolists services and controllers for tenantId handling
# Details:
Update service and controller layers:
- TodoService: Add tenantId parameter to all methods
- ProjectService: Update to pass tenantId to repository
- TodoController: Add @CurrentTenant() to all endpoints
- ProjectController: Update to extract and pass tenantId
- Update business logic for tenant-aware operations

# Test Strategy:
Integration and API tests for Todolists module
