# Task ID: 2
# Title: Update OKRs Module - Objective Repository
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Add tenantId filtering to all Objective repository methods
# Details:
Update ObjectiveRepository methods:
- findAll(): Add tenantId to QueryBuilder WHERE clause
- findById(): Add tenantId to WHERE condition
- create(): Ensure tenantId is set on new entities
- update(): Add tenantId to WHERE condition
- delete(): Add tenantId to WHERE condition
- findByCycleId(): Add tenantId filtering
- findByOwnerId(): Add tenantId filtering
- findByParentId(): Add tenantId filtering
- updateProgress(): Add tenantId to WHERE condition

# Test Strategy:
Unit tests to verify tenantId is included in all queries and cross-tenant access is prevented
