# Task ID: 11
# Title: Update HRM Module - Department and Position Repositories
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Add tenantId filtering to Department and Position repository methods
# Details:
Update repository methods:
- DepartmentRepository: Add tenantId to all queries
- PositionRepository: Add tenantId filtering
- Handle hierarchical relationships with tenantId
- Update organizational chart queries
- Ensure department-employee relationships respect tenant boundaries

# Test Strategy:
Unit tests for Department and Position repositories
